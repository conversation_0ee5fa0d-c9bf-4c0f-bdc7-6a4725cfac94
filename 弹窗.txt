

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <sys/utsname.h>
#import <CommonCrypto/CommonDigest.h>
#import <Security/Security.h>
#import <objc/runtime.h>

#pragma mark - 配置信息
// 卡密验证的APPID
static NSString *const kAppID = @"10002";
// 控制弹窗的APPID（用于后台管理）
static NSString *const kControlAppID = @"10001";
// 在这里修改购买链接
static NSString *const kBuyURL = @"https://www.baidu.com";
// API基础URL
static NSString *const kBaseURL = @"https://jjy.yc88.us.kg/api.php";
// 控制URL
static NSString *const kControlURL = @"https://tckz.yc88.us.kg/control.php";
// UserDefaults Keys
static NSString *const kVerifiedKey = @"KamiVerified";
static NSString *const kKamiKey = @"KamiValue";
static NSString *const kExpireTimeKey = @"ExpireTime";
// Keychain Key
static NSString *const kKeychainIdentifierKey = @"com.kami.deviceid";

#pragma mark - Keychain Helper Methods
static NSString* loadDeviceIDFromKeychain(void) {
    NSMutableDictionary *query = [NSMutableDictionary dictionary];
    query[(__bridge id)kSecClass] = (__bridge id)kSecClassGenericPassword;
    query[(__bridge id)kSecAttrAccount] = kKeychainIdentifierKey;
    query[(__bridge id)kSecReturnData] = @YES;
    query[(__bridge id)kSecMatchLimit] = (__bridge id)kSecMatchLimitOne;
    
    CFDataRef result = NULL;
    OSStatus status = SecItemCopyMatching((__bridge CFDictionaryRef)query, (CFTypeRef *)&result);
    if (status == noErr && result != NULL) {
        NSData *data = (__bridge_transfer NSData *)result;
        return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    }
    return nil;
}

static void saveDeviceIDToKeychain(NSString *deviceID) {
    NSData *data = [deviceID dataUsingEncoding:NSUTF8StringEncoding];
    
    NSMutableDictionary *query = [NSMutableDictionary dictionary];
    query[(__bridge id)kSecClass] = (__bridge id)kSecClassGenericPassword;
    query[(__bridge id)kSecAttrAccount] = kKeychainIdentifierKey;
    
    // 先尝试删除已存在的
    SecItemDelete((__bridge CFDictionaryRef)query);
    
    // 保存新的
    query[(__bridge id)kSecValueData] = data;
    SecItemAdd((__bridge CFDictionaryRef)query, NULL);
}

@interface KamiVerifyManager : NSObject <UITextFieldDelegate>
@property (nonatomic, strong) NSTimer *checkTimer;
@property (nonatomic, weak) UIView *currentAlertView;
+ (instancetype)sharedInstance;
- (void)showVerifyAlert;
- (void)showMessage:(NSString *)message completion:(void(^)(void))completion;
- (void)getNotice:(void(^)(NSString *notice))completion;
- (void)checkInitialKamiStatus;
- (void)startCheckTimer;
- (void)stopCheckTimer;
- (void)checkKamiStatus;
- (NSString *)getDeviceUDID;
- (void)checkControlStatus:(void(^)(BOOL shouldShow))completion;
- (void)shakeView:(UIView *)view;
- (UIViewController *)topViewController;
@end

@implementation KamiVerifyManager

+ (instancetype)sharedInstance {
    static KamiVerifyManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[KamiVerifyManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 立即显示弹窗，不等待状态检查
        [self immediatelyShowVerifyAlert];
        // 异步检查状态
        [self asyncCheckInitialKamiStatus];
    }
    return self;
}

- (void)immediatelyShowVerifyAlert {
    // 检查是否已经验证过
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *savedKami = [defaults stringForKey:kKamiKey];
    if (savedKami) {
        NSTimeInterval expireTime = [defaults doubleForKey:kExpireTimeKey];
        if (expireTime > [[NSDate date] timeIntervalSince1970]) {
            // 如果有有效的卡密，先不显示弹窗
            return;
        }
    }
    
    // 立即显示弹窗
    [self showVerifyAlert];
    
    // 异步检查控制状态
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self checkControlStatus:^(BOOL shouldShow) {
            if (!shouldShow) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    UIView *maskView = [[self topViewController].view viewWithTag:999];
                    [maskView removeFromSuperview];
                });
            }
        }];
    });
}

- (void)asyncCheckInitialKamiStatus {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *savedKami = [defaults stringForKey:kKamiKey];
    
    if (!savedKami) {
        return;
    }
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self checkControlStatus:^(BOOL shouldShow) {
            if (!shouldShow) {
                return;
            }
            
            NSString *urlString = [NSString stringWithFormat:@"%@?api=kmlogon&app=%@&kami=%@&markcode=%@",
                                   kBaseURL, kAppID, savedKami, [self getDeviceUDID]];
            NSURL *url = [NSURL URLWithString:urlString];
            
            NSURLSession *session = [NSURLSession sharedSession];
            [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
                if (error) {
                    NSTimeInterval expireTime = [defaults doubleForKey:kExpireTimeKey];
                    if (expireTime <= [[NSDate date] timeIntervalSince1970]) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [self showVerifyAlert];
                        });
                    }
                    return;
                }
                
                NSError *jsonError;
                NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (jsonError || [json[@"code"] integerValue] != 200) {
                        [defaults removeObjectForKey:kVerifiedKey];
                        [defaults removeObjectForKey:kKamiKey];
                        [defaults removeObjectForKey:kExpireTimeKey];
                        [defaults synchronize];
                        
                        [self showVerifyAlert];
                    } else {
                        NSString *vipTime = json[@"msg"][@"vip"];
                        NSTimeInterval expireTime = [vipTime doubleValue];
                        [defaults setDouble:expireTime forKey:kExpireTimeKey];
                        [defaults synchronize];
                        
                        [self startCheckTimer];
                    }
                });
            }] resume];
        }];
    });
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    self.currentAlertView = nil;
}

- (void)shakeView:(UIView *)view {
    CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"transform.translation.x"];
    animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    animation.duration = 0.5;
    animation.values = @[@(-8), @(8), @(-6), @(6), @(-4), @(4), @(-2), @(2), @(0)];
    [view.layer addAnimation:animation forKey:@"shake"];
}

- (NSString *)getDeviceUDID {
    NSString *deviceID = loadDeviceIDFromKeychain();
    if (deviceID) {
        return deviceID;
    }
    
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    
    NSNumber *totalSpace = nil;
    NSError *error = nil;
    NSDictionary *attrs = [[NSFileManager defaultManager] attributesOfFileSystemForPath:NSHomeDirectory() error:&error];
    if (!error) {
        totalSpace = [attrs objectForKey:NSFileSystemSize];
    }
    
    NSString *uniqueIdentifier = [[[UIDevice currentDevice] identifierForVendor] UUIDString];
    
    NSString *combinedString = [NSString stringWithFormat:@"%@_%@_%@",
                                uniqueIdentifier,
                                deviceModel,
                                totalSpace ?: @"unknown"];
    
    const char *cStr = [combinedString UTF8String];
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    CC_MD5(cStr, (CC_LONG)strlen(cStr), result);
    
    NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [md5String appendFormat:@"%02x", result[i]];
    }
    
    saveDeviceIDToKeychain(md5String);
    return md5String;
}

- (void)getNotice:(void(^)(NSString *notice))completion {
    NSString *urlString = [NSString stringWithFormat:@"%@?api=notice&app=%@", kBaseURL, kAppID];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    config.timeoutIntervalForRequest = 5.0; // 5秒超时
    
    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(@"请输入卡密");
            });
            return;
        }
        
        NSError *jsonError;
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (jsonError) {
                completion(@"请输入卡密");
                return;
            }
            
            if ([json[@"code"] integerValue] == 200) {
                NSString *notice = json[@"msg"][@"app_gg"];
                if (notice && ![notice isEqualToString:@""]) {
                    completion(notice);
                } else {
                    completion(@"请输入卡密");
                }
            } else {
                completion(@"请输入卡密");
            }
        });
    }] resume];
}

- (void)checkControlStatus:(void(^)(BOOL shouldShow))completion {
    NSString *urlString = [NSString stringWithFormat:@"%@?appId=%@", kControlURL, kControlAppID];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    config.timeoutIntervalForRequest = 5.0; // 5秒超时
    
    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(YES); // 获取失败默认显示
            });
            return;
        }
        
        NSError *jsonError;
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (jsonError) {
                completion(YES); // 解析失败默认显示
                return;
            }
            
            BOOL shouldShow = [json[@"data"][@"isShow"] boolValue];
            completion(shouldShow);
        });
    }] resume];
}

- (void)showVerifyAlert {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIView *maskView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        maskView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.2];
        maskView.tag = 999;
        
        // 弹窗样式 - 调整背景色为略微偏灰
        UIView *alertView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 270, 150)];
        alertView.center = CGPointMake(maskView.center.x, maskView.center.y);
        alertView.backgroundColor = [UIColor colorWithRed:0.96 green:0.96 blue:0.96 alpha:1.0];  // 调整为略微偏灰的颜色
        alertView.layer.cornerRadius = 14;
        alertView.clipsToBounds = YES;
        
        [maskView addSubview:alertView];
        
        // 标题
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(16, 15, 238, 25)];
        titleLabel.text = @"验证系统 v1.1";
        titleLabel.textAlignment = NSTextAlignmentCenter;
        titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightSemibold];
        titleLabel.textColor = [UIColor blackColor];
        [alertView addSubview:titleLabel];
        
        // 输入框容器
        UIView *inputContainer = [[UIView alloc] initWithFrame:CGRectMake(15, 55, 240, 40)];
        inputContainer.backgroundColor = [UIColor whiteColor];  // 保持输入框为纯白色
        inputContainer.layer.borderWidth = 0.5;
        inputContainer.layer.borderColor = [UIColor colorWithRed:0.85 green:0.85 blue:0.85 alpha:1.0].CGColor;
        inputContainer.layer.cornerRadius = 5;
        [alertView addSubview:inputContainer];
        
        // 输入框
        UITextField *inputField = [[UITextField alloc] initWithFrame:CGRectMake(10, 0, 220, 40)];
        inputField.placeholder = @"请输入正确的验证码";
        inputField.font = [UIFont systemFontOfSize:14];
        inputField.borderStyle = UITextBorderStyleNone;
        inputField.backgroundColor = [UIColor clearColor];
        inputField.returnKeyType = UIReturnKeyDone;
        inputField.textColor = [UIColor blackColor];
        
        // 设置placeholder颜色和字体
        inputField.attributedPlaceholder = [[NSAttributedString alloc]
            initWithString:@"请输入正确的验证码"
            attributes:@{
                NSForegroundColorAttributeName: [UIColor colorWithRed:0.7 green:0.7 blue:0.7 alpha:1.0],
                NSFontAttributeName: [UIFont systemFontOfSize:14]
            }];
        inputField.delegate = self;
        inputField.tag = 100;
        [inputContainer addSubview:inputField];
        
        // 分隔线
        UIView *separatorLine = [[UIView alloc] initWithFrame:CGRectMake(0, 105, 270, 0.5)];
        separatorLine.backgroundColor = [UIColor colorWithRed:0.85 green:0.85 blue:0.85 alpha:1.0];
        [alertView addSubview:separatorLine];
        
        // 验证按钮
        UIButton *verifyButton = [UIButton buttonWithType:UIButtonTypeSystem];
        verifyButton.frame = CGRectMake(0, 105.5, 270, 44.5);
        [verifyButton setTitle:@"验证" forState:UIControlStateNormal];
        [verifyButton setTitleColor:[UIColor colorWithRed:0.0 green:0.478 blue:1.0 alpha:1.0] forState:UIControlStateNormal];
        verifyButton.titleLabel.font = [UIFont systemFontOfSize:15];
        verifyButton.backgroundColor = [UIColor clearColor];
        
        [verifyButton addTarget:self action:@selector(verifyButtonTouchDown:) forControlEvents:UIControlEventTouchDown];
        [verifyButton addTarget:self action:@selector(verifyButtonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
        [verifyButton addTarget:self action:@selector(verifyButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
        
        [alertView addSubview:verifyButton];
        
        objc_setAssociatedObject(verifyButton, "inputField", inputField, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        objc_setAssociatedObject(verifyButton, "alertView", alertView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        objc_setAssociatedObject(verifyButton, "maskView", maskView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
        self.currentAlertView = alertView;
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(keyboardWillShow:)
                                                     name:UIKeyboardWillShowNotification
                                                   object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(keyboardWillHide:)
                                                     name:UIKeyboardWillHideNotification
                                                   object:nil];
        
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleBackgroundTap:)];
        [maskView addGestureRecognizer:tapGesture];
        
        UIWindow *window = [[UIApplication sharedApplication] keyWindow];
        [window addSubview:maskView];
        
        // 立即呼出键盘
        [inputField becomeFirstResponder];
    });
}

- (void)keyboardWillShow:(NSNotification *)notification {
    if (!self.currentAlertView) return;
    
    NSDictionary *info = [notification userInfo];
    CGRect keyboardFrame = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = keyboardFrame.size.height;
    
    CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;
    CGFloat idealY = (screenHeight - keyboardHeight) / 2 - 50;
    
    CGFloat alertHeight = self.currentAlertView.frame.size.height;
    CGFloat minY = 20;
    CGFloat maxY = screenHeight - keyboardHeight - alertHeight - 20;
    
    idealY = MAX(minY, MIN(idealY, maxY));
    
    [UIView animateWithDuration:0.3 animations:^{
        CGRect frame = self.currentAlertView.frame;
        frame.origin.y = idealY;
        self.currentAlertView.frame = frame;
    }];
}

- (void)keyboardWillHide:(NSNotification *)notification {
    if (!self.currentAlertView) return;
    
    [UIView animateWithDuration:0.3 animations:^{
        self.currentAlertView.center = CGPointMake(self.currentAlertView.superview.center.x, self.currentAlertView.superview.center.y);
    }];
}

- (void)handleBackgroundTap:(UITapGestureRecognizer *)gesture {
    CGPoint location = [gesture locationInView:gesture.view];
    UIView *alertView = gesture.view.subviews.firstObject;
    
    if (!CGRectContainsPoint(alertView.frame, location)) {
        UITextField *inputField = [alertView viewWithTag:100];
        [inputField resignFirstResponder];
    }
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    UIView *alertView = textField.superview.superview;
    for (UIView *subview in alertView.subviews) {
        if ([subview isKindOfClass:[UIButton class]]) {
            UIButton *verifyButton = (UIButton *)subview;
            [self verifyButtonClicked:verifyButton];
            break;
        }
    }
    return YES;
}

- (void)verifyButtonTouchDown:(UIButton *)sender {
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.95, 0.95);
        sender.alpha = 0.7;
    }];
}

- (void)verifyButtonTouchUp:(UIButton *)sender {
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformIdentity;
        sender.alpha = 1.0;
    }];
}

- (void)verifyButtonClicked:(UIButton *)sender {
    UITextField *inputField = objc_getAssociatedObject(sender, "inputField");
    UIView *alertView = objc_getAssociatedObject(sender, "alertView");
    UIView *maskView = objc_getAssociatedObject(sender, "maskView");
    
    NSString *kami = inputField.text;
    kami = [kami stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    if (!kami || [kami isEqualToString:@""]) {
        [self shakeView:alertView];
        return;
    }
    
    NSString *encodedKami = [kami stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    NSString *urlString = [NSString stringWithFormat:@"%@?api=kmlogon&app=%@&kami=%@&markcode=%@",
                          kBaseURL, kAppID, encodedKami, [self getDeviceUDID]];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSession *session = [NSURLSession sharedSession];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (error) {
                [self shakeView:alertView];
                [inputField becomeFirstResponder];
                return;
            }
            
            NSError *jsonError;
            NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
            
            if (jsonError) {
                [self shakeView:alertView];
                [inputField becomeFirstResponder];
                return;
            }
            
            if ([json[@"code"] integerValue] != 200) {
                [self shakeView:alertView];
                [inputField becomeFirstResponder];
            } else {
                NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                [defaults setObject:kami forKey:kKamiKey];
                
                NSString *vipTime = json[@"msg"][@"vip"];
                NSTimeInterval expireTime = [vipTime doubleValue];
                [defaults setDouble:expireTime forKey:kExpireTimeKey];
                [defaults setBool:YES forKey:kVerifiedKey];
                [defaults synchronize];
                
                NSDate *expireDate = [NSDate dateWithTimeIntervalSince1970:expireTime];
                NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
                [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
                NSString *expireDateString = [formatter stringFromDate:expireDate];
                
                [maskView removeFromSuperview];
                [self showMessage:[NSString stringWithFormat:@"验证成功\n到期时间: %@", expireDateString] completion:nil];
                
                [self startCheckTimer];
            }
        });
    }] resume];
}

- (void)showMessage:(NSString *)message completion:(void(^)(void))completion {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"提示"
                                                                     message:message
                                                              preferredStyle:UIAlertControllerStyleAlert];
        
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                         style:UIAlertActionStyleDefault
                                                       handler:^(UIAlertAction * action) {
            if (completion) {
                completion();
            }
        }];
        
        [alert addAction:okAction];
        [[self topViewController] presentViewController:alert animated:YES completion:nil];
    });
}

- (UIViewController *)topViewController {
    UIViewController *rootVC = nil;
    UIWindow *window = nil;
    
    if (@available(iOS 15.0, *)) {
        for (UIWindowScene *windowScene in [[UIApplication sharedApplication] connectedScenes]) {
            if ([windowScene isKindOfClass:[UIWindowScene class]]) {
                for (UIWindow *win in windowScene.windows) {
                    if (win.isKeyWindow) {
                        window = win;
                        break;
                    }
                }
                if (window) break;
            }
        }
    } else {
        for (UIWindow *win in [[UIApplication sharedApplication] windows]) {
            if (win.isKeyWindow) {
                window = win;
                break;
            }
        }
    }
    
    if (!window) {
        window = [[UIApplication sharedApplication] windows].firstObject;
    }
    
    rootVC = window.rootViewController;
    while (rootVC.presentedViewController) {
        rootVC = rootVC.presentedViewController;
    }
    
    return rootVC;
}

- (void)startCheckTimer {
    [self stopCheckTimer];
    
    self.checkTimer = [NSTimer scheduledTimerWithTimeInterval:300.0
                                                      target:self
                                                    selector:@selector(checkKamiStatus)
                                                    userInfo:nil
                                                     repeats:YES];
    
    [[NSRunLoop mainRunLoop] addTimer:self.checkTimer forMode:NSRunLoopCommonModes];
}

- (void)stopCheckTimer {
    [self.checkTimer invalidate];
    self.checkTimer = nil;
}

- (void)checkKamiStatus {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *kami = [defaults stringForKey:kKamiKey];
    if (!kami) return;
    
    NSString *urlString = [NSString stringWithFormat:@"%@?api=kmlogon&app=%@&kami=%@&markcode=%@",
                          kBaseURL, kAppID, kami, [self getDeviceUDID]];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSession *session = [NSURLSession sharedSession];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) return;
        
        NSError *jsonError;
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
        if (jsonError) return;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([json[@"code"] integerValue] != 200) {
                [defaults removeObjectForKey:kVerifiedKey];
                [defaults removeObjectForKey:kKamiKey];
                [defaults removeObjectForKey:kExpireTimeKey];
                [defaults synchronize];
                
                [self stopCheckTimer];
                [self showVerifyAlert];
            } else {
                NSString *vipTime = json[@"msg"][@"vip"];
                NSTimeInterval expireTime = [vipTime doubleValue];
                
                [defaults setDouble:expireTime forKey:kExpireTimeKey];
                [defaults synchronize];
            }
        });
    }] resume];
}

@end

__attribute__((constructor)) static void entry(void) {
    NSLog(@"KamiVerify loaded!");
    dispatch_async(dispatch_get_main_queue(), ^{
        [KamiVerifyManager sharedInstance];
    });
}

